# Inspired by https://pnpm.io/docker#example-2-build-multiple-docker-images-in-a-monorepo
FROM node:20-slim AS base

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

FROM base AS build-prod
COPY . /usr/src/app
WORKDIR /usr/src/app
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
#RUN pnpm run -r build
RUN pnpm run build:web
RUN pnpm deploy --filter=web --prod /prod/web

FROM base AS application

COPY --from=build-prod /prod/web /prod/web
COPY --from=build-prod /usr/src/app/apps/web/.output /prod/web/.output

WORKDIR /prod/web
EXPOSE 3000

CMD [ "pnpm", "start" ]