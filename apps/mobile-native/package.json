{"name": "mobile-native", "version": "0.0.0", "private": true, "type": "module", "packageManager": "pnpm@10.11.0", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "lintts": "eslint . --ext .ts,.vue", "format": "prettier --write ."}, "dependencies": {"ui": "file:../../packages/ui", "core": "file:../../packages/core", "assets": "file:../../packages/assets", "constants": "file:../../packages/constants", "i18n": "file:../../packages/i18n", "stores": "file:../../packages/stores", "@capacitor/android": "^7.2.0", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/device": "^7.0.1", "@capacitor/ios": "^7.2.0", "@mdi/font": "^7.4.47", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuetify": "^3.8.6"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-vuetify": "^2.1.1", "vue-tsc": "^2.2.8"}}