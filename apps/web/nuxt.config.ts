// https://nuxt.com/docs/api/configuration/nuxt-config
import { defineNuxtConfig } from 'nuxt/config'
import path from 'path'
import  i18nConfig  from './i18n.config'
import vuetify from 'vite-plugin-vuetify'

export default defineNuxtConfig({
  app: {
    head: {
      script: [
        {
          src: 'https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4',
          type: 'text/javascript',
          async: true, 
          defer: true, 
        },
      ],
    },
  },
  compatibilityDate: '2025-05-15',
  devtools: { enabled: false },
  ssr: true,
  modules: [
    '@nuxtjs/device',
    ['@nuxtjs/i18n', i18nConfig],
    '@nuxtjs/google-fonts',
  ],
  googleFonts: {
    families: {
      Manrope: [100, 200, 300, 400, 500, 600, 700, 800], // Specify weights
    },
  },
  css: [
    'vuetify/styles',
    '@mdi/font/css/materialdesignicons.css',
    path.resolve(__dirname, '../../packages/assets/styles/tailwind.css'),
    path.resolve(__dirname, '../../packages/assets/styles/global.scss')
  ],
  postcss: {
    plugins: {
      '@tailwindcss/postcss': {},
      autoprefixer: {},
    },
  },
  build: {
    transpile: ['vuetify']
  },

  alias: {
    '@ui': path.resolve(__dirname, '../../packages/ui'),
    '@core': path.resolve(__dirname, '../../packages/core'),
    '@assets': path.resolve(__dirname, '../../packages/assets'),
    '@constants': path.resolve(__dirname, '../../packages/constants'),
    '@i18n': path.resolve(__dirname, '../../packages/i18n'),
    '@stores': path.resolve(__dirname, '../../packages/stores'),
  },
  vite: {
    plugins: [
      vuetify({ autoImport: true })
    ],
    build: {
      rollupOptions: {
        input: path.resolve(__dirname, '../../packages/assets'),
        output: {
          assetFileNames: 'assets/[name].[hash][extname]',
        },
      },
    },
  }
})