<!-- TOC -->
  * [Multi-Platform, based on Vue 3](#multi-platform-based-on-vue-3-)
* [🖥️  Desktop Web (SSR) – built with Nuxt 3](#-desktop-web-ssr--built-with-nuxt-3)
* [📲 Native Mobile App (CSR) – powered by Vue 3 + Capacitor](#-native-mobile-app-csr--powered-by-vue-3--capacitor)
  * [🏗️ Project Structure](#-project-structure)
  * [🚀 Deploy](#-deploy)
  * [🧪 Tech Stack](#-tech-stack)
  * [Install and run](#install-and-run)
    * [Setup SDK's](#setup-sdks)
      * [asdf](#asdf)
    * [Install and run app](#install-and-run-app)
      * [Install](#install)
      * [Run web app (dev mode)](#run-web-app-dev-mode)
      * [Run mobile native app (dev mode)](#run-mobile-native-app-dev-mode)
      * [Mobile build](#mobile-build)
      * [Run web using docker compose in prod mode](#run-web-using-docker-compose-in-prod-mode)
      * [Run web in prod mode](#run-web-in-prod-mode)
<!-- TOC -->

## Multi-Platform, based on Vue 3 
This monorepo powers a unified frontend platform supporting:
# 🖥️  Desktop Web (SSR) – built with Nuxt 3
# 📲 Native Mobile App (CSR) – powered by Vue 3 + Capacitor
All applications share common Vue components, logic, and assets.


## 🏗️ Project Structure
client-app/
├── apps/
│ ├── web/ # Nuxt 3 SSR app for desktop and mobile browsers
│ └── mobile-native/ # Vue 3 SPA wrapped with Capacitor for native devices
│
├── packages/
  ├── assets/ # Shared static assets and styles
  ├── constants/ # Shared constants
  ├── core/ # Shared logic (composables, utilities)
  ├── i18n/ # Shared localizations
  ├── stores/ # Shared Pinia stores
  ├── ui/ # Shared Vue 3 components
      |── components/ # simple one purpose ui components
	    |   |── common
	    |   |── desktop
	    |   |── mobile
      |── layouts/ #abstract templates, that combine few components or layouts and can be used in page or another layout
		  |   |──common
		  |   |──desktop
		  |   |──mobile
      |──pages/ *vue modules, which may be used as whole page
		      |──common/ #pages, which can be used both in mobile and desktop
		      |──desktop/ #pages, which can be used only in desktop
		      |──mobile/ #pages, which can be used only in mobile

## 🚀 Deploy

- `web`: SSR-ready for **Vercel**, **Netlify**, or Node
- `mobile-native`: Uses **Capacitor** for Android/iOS native builds

## 🧪 Tech Stack

- Nuxt 3 + Vue 3
- Capacitor.js
- ESLint + Prettier
- Vite (for native app)
- Tailwind CSS (temporary used with CDN)


## Install and run

### Setup SDK's
#### asdf

```shell
 asdf plugin add pnpm https://github.com/jonathanmorley/asdf-pnpm
```

```shell
asdf install
```

### Install and run app
#### Install
In root:
```bash
pnpm install
```

#### Run web app (dev mode)
```bash
cd apps/web/
pnpm dev # runs dev server on http://localhost:3000/
```
other scripts
```bash
pnpm build/dev/start/generate/preview/postinstall/lint/format
```

#### Run mobile native app (dev mode)
```bash
cd apps/mobile-native/
pnpm dev # runs dev server of mobile app on http://localhost:5173/
```

#### Mobile build
```bash
pnpm build
npx cap copy
npx cap open android   # or ios
```

#### Run web using docker compose in prod mode
```shell
docker compose -f compose.app.yml up -d --build
```

#### Run web in prod mode
Build:
```shell
rm -rf ./dist
pnpm run build:web
pnpm deploy --filter=web --prod ./dist/prod/web
cp -r ./apps/web/.output ./dist/prod/web
```
Run:
```shell
cd ./dist/prod/web
pnpm start
```