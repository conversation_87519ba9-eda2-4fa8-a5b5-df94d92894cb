{"name": "i18n", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.11.0", "dependencies": {"constants": "file:../constants", "@mdi/font": "^7.4.47", "@nuxtjs/device": "3.2.4", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/i18n": "^9.5.4", "express-session": "^1.18.1", "nuxt": "^3.17.3", "vue": "^3.5.14", "vue-router": "^4.5.1", "vuetify": "^3.8.6"}}